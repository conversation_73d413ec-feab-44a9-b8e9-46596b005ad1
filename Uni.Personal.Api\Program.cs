using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using Uni.Personal.BLL;
using Uni.Personal.DAL;
using UNI.Common.CommonBase;

namespace Uni.Personal.Api
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddHttpContextAccessor();
            builder.Services.AddScoped<IUniCommonBaseRepository, UniCommonBaseRepository>();
            builder.Services.RegisterServices();
            builder.Services.RegisterRepositories();

            // Configure JWT Authentication
            var jwtSettings = builder.Configuration.GetSection("Jwt");
            var swaggerSettings = builder.Configuration.GetSection("Swagger");
            var cors = builder.Configuration.GetSection("Cors");
            builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.Authority = jwtSettings["Authority"];
                    options.Audience = jwtSettings["Audience"];
                    options.RequireHttpsMetadata = jwtSettings.GetValue<bool>("RequireHttpsMetadata");
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = jwtSettings.GetValue<bool>("ValidateIssuer"),
                        ValidateAudience = jwtSettings.GetValue<bool>("ValidateAudience"),
                        ValidateLifetime = jwtSettings.GetValue<bool>("ValidateLifetime"),
                        ValidateIssuerSigningKey = true,
                        ClockSkew = TimeSpan.Zero
                    };
                });

            //cors
            builder.Services.AddCors(options =>
            {
                //check if has configuration
                if (cors.GetSection("Origins").Get<string[]>() == null)
                {
                    options.AddPolicy("CorsPolicy",
                        policy =>
                        {
                            policy.AllowAnyOrigin()
                                .AllowAnyHeader()
                                .AllowAnyMethod();
                        });
                }
                else
                {
                    options.AddPolicy("CorsPolicy",
                        policy =>
                        {
                            policy.WithOrigins(cors.GetSection("Origins").Get<string[]>() ?? new[] { "http://localhost:5255" })
                                .AllowAnyHeader()
                                .AllowAnyMethod();
                        });
                }
            });

            builder.Services.AddAuthorization();
            builder.Services.AddControllers();
            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Uni Personal Portal API", Version = "v1" });

                // Add OIDC Authentication to Swagger
                // Define the OAuth2.0 scheme that's in use (implicit flow)
                c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.OAuth2,
                    OpenIdConnectUrl = new Uri($"{jwtSettings["Authority"]}/.well-known/openid-configuration"),
                    
                    Flows = new OpenApiOAuthFlows
                    {
                        Implicit = new OpenApiOAuthFlow
                        {
                            AuthorizationUrl = new Uri($"{jwtSettings["Authority"]}/protocol/openid-connect/auth"),
                            TokenUrl = new Uri($"{jwtSettings["Authority"]}/protocol/openid-connect/token"),
                            Scopes = new Dictionary<string, string>
                        {
                            { "openid", "OpenID Connect scope" },
                            { "profile", "Profile information" },
                            { "email", "Email address" }
                        }
                        }
                    }
                });
                // Enforce security requirements globally
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "oauth2"
                            }
                        },
                        Array.Empty<string>()
                        // new[] { "openid", "profile", "email" }
                    }
                });
            });

            builder.Host.UseSerilog((context, services, configuration) =>
            {
                configuration.ReadFrom.Configuration(context.Configuration)
                    .ReadFrom.Services(services)
                    .Enrich.FromLogContext();
            });

            var app = builder.Build();



            // Configure the HTTP request pipeline.
            //TODO: Uncomment the following lines to enable Swagger in development mode only
            //if (app.Environment.IsDevelopment())
            //{
            app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    var clientId = swaggerSettings["client_id"] ?? "swagger_developer";
                    var scopes = swaggerSettings.GetSection("scopes").Get<string[]>() ?? new[] { "openid", "profile", "email" };

                    c.OAuthClientId(clientId);
                    c.OAuthUsePkce();
                    c.OAuthScopes(scopes);
                    c.OAuthUseBasicAuthenticationWithAccessCodeGrant();
                });
            //}
            // Error handler
            app.UseExceptionHandler(handler =>
            {
                handler.Run(async context =>
                {
                    var exception = context.Features.Get<IExceptionHandlerFeature>()?.Error;
                    context.Response.StatusCode = 500; // Internal Server Error
                    context.Response.ContentType = "application/json";
                    var errorResponse = new
                    {
                        StatusCode = 500,
                        Message = "An unexpected error occurred. Please try again later."
                    };
                    await context.Response.WriteAsJsonAsync(errorResponse);
                });
            });
            app.UseCors("CorsPolicy");
            app.UseHttpsRedirection();
            app.UseAuthentication();
            app.UseAuthorization();
            app.MapControllers();
            app.Run();
        }
    }
}
