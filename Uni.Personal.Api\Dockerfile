# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080
EXPOSE 8081


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["uni-common/Nuget.Config", "uni-common/"]
COPY ["Uni.Personal.Api/Uni.Personal.Api.csproj", "Uni.Personal.Api/"]
COPY ["uni-common/UNI.Model/UNI.Model.csproj", "uni-common/UNI.Model/"]
COPY ["uni-common/UNI.Utils/UNI.Utils.csproj", "uni-common/UNI.Utils/"]
COPY ["Uni.Personal.BLL/Uni.Personal.BLL.csproj", "Uni.Personal.BLL/"]
COPY ["Uni.Personal.DAL/Uni.Personal.DAL.csproj", "Uni.Personal.DAL/"]
COPY ["Uni.Personal.Model/Uni.Personal.Model.csproj", "Uni.Personal.Model/"]
COPY ["uni-common/UNI.Common/UNI.Common.csproj", "uni-common/UNI.Common/"]
RUN dotnet restore "./Uni.Personal.Api/Uni.Personal.Api.csproj"
COPY . .
WORKDIR "/src/Uni.Personal.Api"
RUN dotnet build "./Uni.Personal.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Uni.Personal.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Uni.Personal.Api.dll"]