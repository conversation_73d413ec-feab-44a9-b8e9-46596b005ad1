<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>a2744f31-97c6-4ed2-bfb6-55c477bd298f</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.16" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1-Preview.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\uni-common\UNI.Model\UNI.Model.csproj" />
    <ProjectReference Include="..\Uni.Personal.BLL\Uni.Personal.BLL.csproj" />
    <ProjectReference Include="..\Uni.Personal.Model\Uni.Personal.Model.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="logs\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Properties\PublishProfiles\webdebloy_dev.pubxml.user" />
  </ItemGroup>

</Project>
