﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>UNI.Common</AssemblyName>
    <PackageId>UNI.Common</PackageId>    
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Elastic.Apm" Version="1.25.3" />
    <PackageReference Include="Google.Cloud.Firestore" Version="3.8.0" />
	  <PackageReference Include="Google.Cloud.PubSub.V1" Version="3.9.0" />
	  <PackageReference Include="Google.Cloud.Storage.V1" Version="4.7.0" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection" Version="6.0.25" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.1.34" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.1.38" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.ViewFeatures" Version="2.1.3" />
    <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.1.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Xml" Version="8.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols" Version="7.3.1" />
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="2.3.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NSwag.Generation" Version="13.6.2" />
    <PackageReference Include="RestSharp" Version="106.15.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="4.7.0" />
    <PackageReference Include="System.ComponentModel.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.IO.FileSystem" Version="4.3.0" />
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
    <PackageReference Include="System.Xml.XDocument" Version="4.3.0" />
    <PackageReference Include="System.Xml.XmlDocument" Version="4.3.0" />
    <PackageReference Include="System.Xml.XmlSerializer" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\UNI.Model\UNI.Model.csproj" />
    <ProjectReference Include="..\UNI.Utils\UNI.Utils.csproj" />
  </ItemGroup>

</Project>
